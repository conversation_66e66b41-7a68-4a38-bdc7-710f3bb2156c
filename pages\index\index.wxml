<!--index.wxml-->
<view class="demo">
    <view class="title">
        <text></text>
        <!-- <text>CT221D-0D4D<br/>CT221D-0D4D CT221D-0D4D</text> -->
        <view class="namedevice">
            <text>{{blename}}</text><text>{{deviceId}}</text>
        </view>
        <text bindtap="goBle"
            class="blueconnect">{{blename ? "断开连接" : "蓝牙连接"}}</text>
    </view>
    <!-- 发送数据 -->
    <view class="data-title">发送数据：</view>
    <scroll-view class="scrollarea" scroll-y type="list">
        <view wx:for="{{sendData}}" wx:key="index" class="item">
            {{item}}
        </view>
    </scroll-view>
    <!-- 接收数据 -->
    <view class="data-title">接收数据：</view>
    <scroll-view class="scrollarea" scroll-y type="list">
        <view wx:for="{{acceptData}}" wx:key="index" class="item">
            {{item}}
        </view>
    </scroll-view>
    <view class="btn">
        <button bindtap="clearData">清空</button>
    </view>
    <!-- 查询指令 -->
    <view class="instruct">
        <text bindtap="toggleCollapse" class="title">查询指令</text>
        <view wx:if="{{isCollapsed}}" class="content">
            <view
                wx:for="{{queryCommand}}"
                wx:key="index"
                bindtap="queryCTPL"
                data-item="{{item}}"
                data-index="{{index}}"
                class="{{data.queryIndex === index ? 'text active' : 'text'}}">
                {{item}}
            </view>
        </view>
    </view>
    <!-- 设置指令 -->
    <view class="instruct">
        <text bindtap="toggleCollapse1" class="title">设置指令</text>
        <view wx:if="{{isCollapsed1}}" class="content">
            <view
                wx:for="{{settingCommand}}"
                wx:key="index"
                class="text"
                bindtap="setCTPL"
                data-index="{{index}}">{{item}}
            </view>
        </view>
    </view>
    <!-- 打印指令 -->
    <view class="instruct">
        <text bindtap="toggleCollapse2" class="title">打印指令</text>
        <view wx:if="{{isCollapsed2}}" class="content">
            <view
                wx:for="{{printCommand}}"
                wx:key="index"
                class="text"
                bindtap="printCTPL"
                data-index="{{index}}">{{item}}
            </view>
        </view>
    </view>

    <!-- esc指令 -->
    <view class="instruct">
        <text bindtap="toggleCollapse3" class="title">ESC指令</text>
        <view wx:if="{{isCollapsed3}}" class="content">
            <view
                wx:for="{{escCommand}}"
                wx:key="index"
                class="text"
                bindtap="escPrintCTPL"
                data-index="{{index}}">{{item}}
            </view>
        </view>
    </view>

    <!-- 设置模式 -->
    <view class="custom-dialog" wx:if="{{ showDialog }}">
        <!-- 弹窗内容 -->
        <view class="popup-cont">
            <view class="popup-list">
                <input
                    bindinput="inputChange"
                    value="{{ printMode }}"
                    type="number"
                    data-name="printMode"
                    placeholder="请输入参数0~3"
                    wx:if="{{ settingIndex === 0 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ paperType }}"
                    type="number"
                    data-name="paperType"
                    placeholder="请输入参数0~2"
                    wx:if="{{ settingIndex === 1 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ memoryPrint }}"
                    type="number"
                    data-name="memoryPrint"
                    placeholder="请输入参数0~1"
                    wx:if="{{ settingIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ autoshutDown }}"
                    type="number"
                    data-name="autoshutDown"
                    placeholder="请输入参数1~60min"
                    wx:if="{{ settingIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ density }}"
                    type="number"
                    data-name="density"
                    placeholder="请输入打印浓度0~15"
                    wx:if="{{ settingIndex === 5 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ printSpeed }}"
                    type="number"
                    data-name="printSpeed"
                    placeholder="请输入打印速度1~5"
                    wx:if="{{ settingIndex === 6 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ printDirection }}"
                    type="number"
                    data-name="printDirection"
                    placeholder="请输入打印方向0或1"
                    wx:if="{{ settingIndex === 7 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ printMirror }}"
                    type="number"
                    data-name="printMirror"
                    placeholder="请输入打印镜像0或1"
                    wx:if="{{ settingIndex === 7 }}" />
            </view>
        </view>
        <!-- 关闭按钮 -->
        <view class="dialog-btn">
            <text class="cancel" bindtap="close">取消</text>
            <text class="confirm" bindtap="confirm">确定</text>
        </view>
    </view>

    <!-- 打印模式 -->
    <view class="custom-dialog" wx:if="{{ showDialog1 }}">
        <view class="popup-cont">
            <view class="popup-list">
                <input
                    bindinput="inputChange"
                    value="{{ paperObj.width }}"
                    type="number"
                    data-name="paperObj.width"
                    placeholder="请输入宽度"
                    wx:if="{{ printIndex === 0 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ paperObj.height }}"
                    type="number"
                    data-name="paperObj.height"
                    placeholder="请输入高度"
                    wx:if="{{ printIndex === 0 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ lineObj.x }}"
                    type="number"
                    data-name="lineObj.x"
                    placeholder="请输入线条起始点x"
                    wx:if="{{ printIndex === 1 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ lineObj.y }}"
                    type="number"
                    data-name="lineObj.y"
                    placeholder="请输入线条起始点y"
                    wx:if="{{ printIndex === 1 }}" />
                <input
                    bindinput="inputChange"
                    value="{{ lineObj.width }}"
                    type="number"
                    data-name="lineObj.width"
                    placeholder="请输入线条长度"
                    wx:if="{{ printIndex === 1 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{ lineObj.height }}"
                    data-name="lineObj.height"
                    placeholder="请输入线条高度"
                    wx:if="{{ printIndex === 1 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{ textObj.x }}"
                    data-name="textObj.x"
                    placeholder="请输入文字起始点x"
                    wx:if="{{ printIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{ textObj.y }}"
                    data-name="textObj.y"
                    placeholder="请输入文字起始点y"
                    wx:if="{{ printIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{textObj.rotation}}"
                    data-name="textObj.rotation"
                    placeholder="请输入旋转方向"
                    wx:if="{{ printIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{textObj.xRatio}}"
                    data-name="textObj.xRatio"
                    placeholder="请输入x轴放大倍率"
                    wx:if="{{ printIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{textObj.yRatio}}"
                    data-name="textObj.yRatio"
                    placeholder="请输入y轴放大倍率"
                    wx:if="{{ printIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    type="text"
                    value="{{textObj.text}}"
                    data-name="textObj.text"
                    placeholder="请输入要打印的文本内容"
                    wx:if="{{ printIndex === 2 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{barcodeObj.x}}"
                    data-name="barcodeObj.x"
                    placeholder="请输入条码起始点x"
                    wx:if="{{ printIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{barcodeObj.y}}"
                    data-name="barcodeObj.y"
                    placeholder="请输入条码起始点y"
                    wx:if="{{ printIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    value="{{barcodeObj.codeType}}"
                    data-name="barcodeObj.codeType"
                    placeholder="请输入条码类型"
                    wx:if="{{printIndex === 3}}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{barcodeObj.height}}"
                    data-name="barcodeObj.height"
                    placeholder="请输入条码高度"
                    wx:if="{{ printIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{barcodeObj.displayBarCode}}"
                    data-name="barcodeObj.displayBarCode"
                    placeholder="条码是否展示码文以及码文对齐方式"
                    wx:if="{{printIndex === 3}}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{barcodeObj.rotation}}"
                    data-name="barcodeObj.rotation"
                    placeholder="请输入旋转角度"
                    wx:if="{{ printIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{barcodeObj.narrow}}"
                    data-name="barcodeObj.narrow"
                    placeholder="请输入窄比例因子"
                    wx:if="{{ printIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    type="text"
                    value="{{barcodeObj.content}}"
                    data-name="barcodeObj.content"
                    placeholder="请输入条码内容"
                    wx:if="{{ printIndex === 3 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{qrcodeObj.x}}"
                    data-name="qrcodeObj.x"
                    placeholder="请输入二维码起始点x"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{qrcodeObj.y}}"
                    data-name="qrcodeObj.y"
                    placeholder="请输入二维码起始点y"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    value="{{qrcodeObj.eccLevel}}"
                    data-name="qrcodeObj.eccLevel"
                    placeholder="请输入纠错等级"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{qrcodeObj.cellWidth}}"
                    data-name="qrcodeObj.cellWidth"
                    placeholder="请输入二维码格子宽度1~10"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    value="{{qrcodeObj.encodeMode}}"
                    data-name="qrcodeObj.encodeMode"
                    placeholder="二维码编码模式"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    value="{{qrcodeObj.codeMode}}"
                    data-name="qrcodeObj.codeMode"
                    placeholder="请输入二维码码制"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    value="{{qrcodeObj.mask}}"
                    data-name="qrcodeObj.mask"
                    placeholder="请输入蒙版等级"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    type="text"
                    value="{{qrcodeObj.content}}"
                    data-name="qrcodeObj.content"
                    placeholder="请输入二维码内容"
                    wx:if="{{ printIndex === 4 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{bitMapObj.x}}"
                    data-name="bitMapObj.x"
                    placeholder="请输入图片起始点x"
                    wx:if="{{ printIndex === 5 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{bitMapObj.y}}"
                    data-name="bitMapObj.y"
                    placeholder="请输入图片起始点y"
                    wx:if="{{ printIndex === 5 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{compressBitMapObj.x}}"
                    data-name="compressBitMapObj.x"
                    placeholder="请输入图片起始点x"
                    wx:if="{{ printIndex === 6 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{compressBitMapObj.y}}"
                    data-name="compressBitMapObj.y"
                    placeholder="请输入图片起始点y"
                    wx:if="{{ printIndex === 6 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{compressBitMapObj.dpi}}"
                    data-name="compressBitMapObj.dpi"
                    placeholder="请输入打印机dpi分辨率"
                    wx:if="{{ printIndex === 6 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{shiftX}}"
                    data-name="shiftX"
                    placeholder="偏移横坐标-203 到 203"
                    wx:if="{{ printIndex === 9 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{shiftY}}"
                    data-name="shiftY"
                    placeholder="偏移横坐标-203 到 203"
                    wx:if="{{ printIndex === 9 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{reverseX}}"
                    data-name="reverseX"
                    placeholder="请输入x轴起始坐标"
                    wx:if="{{ printIndex === 10 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{reverseY}}"
                    data-name="reverseY"
                    placeholder="请输入y轴起始坐标"
                    wx:if="{{ printIndex === 10 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{reverseWidth}}"
                    data-name="reverseWidth"
                    placeholder="输入区域反白的宽度"
                    wx:if="{{ printIndex === 10 }}" />
                <input
                    bindinput="inputChange"
                    type="number"
                    value="{{reverseHeight}}"
                    data-name="reverseHeight"
                    placeholder="输入区域反白的高度"
                    wx:if="{{ printIndex === 10 }}" />
            </view>
        </view>
        <!-- 关闭按钮 -->
        <view class="dialog-btn">
            <text class="cancel" bindtap="close1">取消</text>
            <text class="confirm" bindtap="confirm1">确定</text>
        </view>
    </view>
    <canvas canvas-id="canvas" id="canvas"
        style="width: 400px; height: 400px"></canvas>
</view>