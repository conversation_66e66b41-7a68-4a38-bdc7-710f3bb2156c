// index.js
import CTPL from "../../web-CTPL-SDK/ctpl.js"

Page({

    /**
     * 页面的初始数据
     */
    data: {
        sendData: [], //发送数据
        acceptData: [], //接收数据
        queryCommand: [
            "查询硬件配置（合并）",
            "查询打印机型号",
            "查询是否支持压缩算法",
            "查询固件信息（合并）",
            "查询纸张类型",
            "查询记忆打印状态",
            "查询打印浓度",
            "查询打印速度",
            "查询自动关机（分钟）",
            "查询展示参数（合并）",
            "查询硬件版本号",
            "查询当前电量",
            "查询打印模式",
            "查询打印机状态"
        ], //查询指令
        queryIndex: -1,
        settingCommand: [
            "设置打印模式",
            "设置纸张类型",
            "设置记忆打印",
            "设置自动关机（分钟）",
            "恢复出厂设置",
            "设置打印浓度",
            "设置打印速度",
            "设置打印方向和镜像",
        ], //设置指令
        settingIndex: -1,
        printCommand: [
            "设置纸张的宽度跟高度",
            "绘制线条",
            "绘制文字",
            "绘制条码",
            "绘制二维码",
            "绘制图片",
            "绘制压缩图片",
            "执行打印",
            "打印自检页",
            "设置标签图像的横纵坐标",
            "将指定区域的图像缓存反白",
            "走纸一张",
            "连续打印3张"
        ], //打印指令
        escCommand: [
            "打印文本",
            "打印条码",
            "打印二维码",
            "打印线条",
            "打印图片",
        ],
        otaCommand: ["固件升级"],
        printMode: 0, //标签模式/间隙纸，标签模式/连续纸，标签模式/黑标纸，票据模式/连续纸
        paperType: 0, //设置纸张类型
        memoryPrint: 0, //记忆打印
        autoshutDown: 1, //自动睡眠
        density: 1, //打印浓度
        printSpeed: 1, //打印速度
        printDirection: 0, //打印方向
        printMirror: 0, //打印镜像
        shiftX: 0, // 偏移横坐标
        shiftY: 0, //偏移纵坐标
        reverseX: 0, // 图像缓存反白指定x起始坐标
        reverseY: 0, //图像缓存反白指定y起始坐标
        reverseWidth: 0, // 图像缓存反白x坐标宽度
        reverseHeight: 0, //图像缓存反白y坐标宽度
        printIndex: -1, //打印index
        //打印图元
        pelData: [],
        //新的图片高度
        newHeight: "",
        paperObj: {
            width: "40",
            height: "30",
        },
        lineObj: {
            type: "line",
            x: 8,
            y: 4,
            width: 0,
            height: 0,
        },
        textObj: {
            type: "text",
            x: 16,
            y: 48,
            rotation: 0,
            xRatio: 1,
            yRatio: 1,
            text: "编辑文本123456",
        },
        barcodeObj: {
            type: "barcode",
            x: 8,
            y: 88,
            codeType: 128,
            height: 0,
            displayBarCode: 1,
            rotation: 0,
            narrow: 5,
            content: 1234567890,
        },
        qrcodeObj: {
            type: "qrcode",
            x: 16,
            y: 160,
            eccLevel: "H",
            cellWidth: 1,
            encodeMode: "M",
            codeMode: "M1",
            mask: "S7",
            content: 1234567890,
        },
        bitMapObj: {
            type: "bitmap",
            x: 0,
            y: 0,
        },
        compressBitMapObj: {
            type: "compressbitmap",
            x: 0,
            y: 0,
            dpi: 8,
        },
        imageData: "",
        imagePath: "/images/tupian.png",
        imagePath1: '/images/40x30.png',
        blename: '',
        deviceId: '',
        //图片数据
        imageRes: {},
        showDialog: false,
        showDialog1: false,
        isCollapsed: false, // 初始状态为折叠
        isCollapsed1: false, // 初始状态为折叠
        isCollapsed2: true, // 初始状态为折叠
        connectionStatus: true, //蓝牙连接状态
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        const $this = this
        this.setData({ blename: options.blename ? `${options.blename}` : "" })
        this.setData({ deviceId: options.deviceId ? `${options.deviceId}` : "" })
        wx.onBLEConnectionStateChange(function (res) {
            if (!res.connected) {
                // 设备已断开连接，处理相关逻辑
                $this.setData({
                    blename: "",
                    deviceId: ''
                })
                wx.closeBluetoothAdapter()
            }
        })
        wx.request({
            url: "https://dlabelweb.mydlabel.com/CT230B-V12.bin",
            method: "GET",
            responseType: "arraybuffer",
            success: (res) => {
                // res.data 包含了获取到的文件数据
                this.otaArrayBuffer = new Uint8Array(res.data)
            },
            fail: (error) => {
                console.error("获取文件数据失败", error)
            },
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    inputChange(event) {
        const { name } = event.currentTarget.dataset
        const { value } = event.detail
        this.setData({
            [`${name}`]: value
        })
    },
    // 点击按钮切换折叠状态
    toggleCollapse() {
        this.setData({
            isCollapsed: !this.data.isCollapsed,
        })
    },
    toggleCollapse1() {
        this.setData({
            isCollapsed1: !this.data.isCollapsed1,
        })
    },
    toggleCollapse2() {
        this.setData({
            isCollapsed2: !this.data.isCollapsed2,
        })
    },
    toggleCollapse3() {
        this.setData({
            isCollapsed3: !this.data.isCollapsed3,
        })
    },
    toggleCollapse4() {
        this.setData({
            isCollapsed4: !this.data.isCollapsed4,
        })
    },
    //连接蓝牙
    goBle() {
        if (this.data.blename) {
            CTPL.disconnect()
            this.setData({
                blename: "",
            })
        } else {
            wx.navigateTo({
                url: "/pages/connect/connect",
            })
        }
    },
    //查询指令
    async queryCTPL(event) {
        const { item, index } = event.currentTarget.dataset
        this.data.queryIndex = index
        if (index === 0) {
            //查询硬件配置（合并）
            const res = await CTPL.queryHardwareConfig()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`硬件配置信息：${JSON.stringify(res)}`),
            })
        } else if (index === 1) {
            //查询打印机型号
            const res = await CTPL.queryHardwareModel()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`打印机型号：${res}`),
            })
        } else if (index === 2) {
            // 查询支持压缩算法
            const res = await CTPL.queryCompressPrintCompletion()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`支持压缩算法：${res}`),
            })
        } else if (index === 3) {
            // 查询固件信息（合并）
            const res = await CTPL.queryFirmwareInfo()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`固件信息：${JSON.stringify(res)}`),
            })
        } else if (index === 4) {
            // 查询纸张类型
            const res = await CTPL.queryPaperType()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`纸张类型：${res}`),
            })
        } else if (index === 5) {
            // 查询记忆打印状态
            const res = await CTPL.queryMemoryPrint()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`记忆打印状态：${res}`),
            })
        } else if (index === 6) {
            // 查询打印浓度
            const res = await CTPL.queryDensity()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`打印浓度：${res}`),
            })
        } else if (index === 7) {
            // 查询打印速度
            const res = await CTPL.querySpeed()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`打印速度：${res}`),
            })
        } else if (index === 8) {
            // 查询自动关机（分钟）
            const res = await CTPL.queryAutoShutDown()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`自动关机（分钟）：${res}`),
            })
        } else if (index === 9) {
            // 查询展示参数（合并）
            const res = await CTPL.queryDisplayInfo()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`展示参数（合并）：${JSON.stringify(res)}`),
            })
        } else if (index === 10) {
            // 查询硬件版本号
            const res = await CTPL.queryHardwareVersion()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`硬件版本号：${res}`),
            })
        } else if (index === 11) {
            // 查询当前电量
            const res = await CTPL.queryBattery()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`当前电量：${res}`),
            })
        } else if (index === 12) {
            // 查询打印模式
            const res = await CTPL.queryPrintMode()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`打印模式：${res}`),
            })
        } else if (index === 13) {
            // 查询打印机状态
            const res = await CTPL.printStateInfo()
            this.setData({
                sendData: this.data.sendData.concat(item),
                acceptData: this.data.acceptData.concat(`打印机状态：${JSON.stringify(res)}`),
            })
        }
    },
    //设置指令
    async setCTPL(event) {
        const { index } = event.currentTarget.dataset
        this.setData({
            settingIndex: index,
        })
        if (index === 4) {
            CTPL.resetFirmware()
        } else {
            this.setData({
                showDialog: true,
            })
        }
    },
    //打印指令
    printCTPL(event) {
        const { index } = event.currentTarget.dataset
        this.setData({
            printIndex: index,
        })
        if (this.data.printIndex === 7) {
            this.print()
        } else if (this.data.printIndex === 8) {
            CTPL.deviceSelfTestPage()
        } else if (this.data.printIndex === 11) {
            CTPL.formFeed()
        } else if (this.data.printIndex === 12) {
            wx.showLoading()
            this.processPrint([1, 2, 3])
        } else {
            this.setData({
                showDialog1: true,
            })
        }
    },
    //ESC指令测试
    escPrintCTPL(event) {
        const { index } = event.currentTarget.dataset
        this.setData({
            printIndex: index,
        })
        if (this.data.printIndex === 0) {
            //打印文本
            CTPL.escInitPrinter()
            CTPL.escDrawText(1, 0, 0, "这是一段测试文本")
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escDrawText(1, 0, 0, "AAAAAAAAAA")
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escNextLine()
            CTPL.escDrawText(1, 0, 0, "BBBBBBBBBBBBB")
            CTPL.escExcute()
        } else if (this.data.printIndex === 1) {
            //打印条码
            CTPL.escInitPrinter()
            CTPL.escDrawBarCode(2, 4, 60, "123456789")
            CTPL.escExcute()
        } else if (this.data.printIndex === 2) {
            //打印二维码
            CTPL.escInitPrinter()
            CTPL.escDrawQRCode(9, "测试打印二维码")
            CTPL.escExcute()
        } else if (this.data.printIndex === 3) {
            //打印横线
            CTPL.escInitPrinter()
            CTPL.escDrawLine(464, 16)
            CTPL.escExcute()
        } else if (this.data.printIndex === 4) {
            //打印图形
            CTPL.escInitPrinter()
            this.getImageData((res) => {
                CTPL.escDrawBitMap(
                    res.data,
                    res.width,
                    res.height
                )
                CTPL.escExcute(() => { })
            })
        }
    },
    async otaUpdate() {
        // const isSuccess = await CTPL.executeFirmwareUpdate(this.otaArrayBuffer);
        const $this = this
        wx.showLoading({
            title: "正在升级请稍候...",
        })
        CTPL.executeFirmwareUpdate(
            this.otaArrayBuffer,
            function (progress, updateSuccessed) {
                if (updateSuccessed) {
                    wx.hideLoading()
                    wx.showToast({
                        title: "升级成功",
                        duration: 2000,
                    })
                }
            }
        )
    },
    close() {
        this.setData({
            showDialog: false,
        })
    },
    close1() {
        this.setData({
            showDialog1: false,
        })
    },
    async confirm() {
        if (this.data.settingIndex === 0) {
            //设置打印模式
            CTPL.setPrintMode(this.data.printMode)
            this.setData({
                sendData: this.data.sendData.concat(`设置打印模式：${this.data.printMode}`),
            })
        } else if (this.data.settingIndex === 1) {
            //设置纸张类型
            CTPL.setPaperType(this.data.paperType)
            this.setData({
                sendData: this.data.sendData.concat(`设置纸张类型：${this.data.paperType}`),
            })
        } else if (this.data.settingIndex === 2) {
            // 设置记忆打印
            CTPL.setMemoryPrint(this.data.memoryPrint)
            this.setData({
                sendData: this.data.sendData.concat(`设置记忆打印：${this.data.memoryPrint}`),
            })
        } else if (this.data.settingIndex === 3) {
            // 设置自动关机（分钟）
            CTPL.setAutoShutdown(this.data.autoshutDown)
            this.setData({
                sendData: this.data.sendData.concat(`设置自动关机：${this.data.autoshutDown}`),
            })
        } else if (this.data.settingIndex === 5) {
            // 设置打印浓度
            CTPL.setDensity(this.data.density)
            this.setData({
                sendData: this.data.sendData.concat(`设置打印浓度：${this.data.density}`),
            })
        } else if (this.data.settingIndex === 6) {
            // 设置打印速度
            CTPL.setSpeed(this.data.printSpeed)
            this.setData({
                sendData: this.data.sendData.concat(`设置打印速度：${this.data.printSpeed}`),
            })
        } else if (this.data.settingIndex === 7) {
            // 设置打印方向
            CTPL.setDirection(this.data.printDirection, this.data.printMirror)
            this.setData({
                sendData: this.data.sendData.concat(`设置打印方向和镜像：${this.data.printDirection},${this.data.printMirror}`),
            })
        }
        this.setData({
            showDialog: false,
        })
    },
    confirm1() {
        if (this.data.printIndex === 0) {
            //设置纸张的宽度跟高度
            this.setData({
                sendData: this.data.sendData.concat(`设置纸张的宽度跟高度：${this.data.paperObj.width},${this.data.paperObj.height}`),
            })
        } else if (this.data.printIndex === 1) {
            //绘制线条
            this.setData({
                pelData: this.data.pelData.concat(this.data.lineObj),
                sendData: this.data.sendData.concat(`绘制线条：${this.data.lineObj.x},${this.data.lineObj.y},${this.data.lineObj.width},${this.data.lineObj.height}`),
            })
        } else if (this.data.printIndex === 2) {
            // 绘制文字
            this.setData({
                pelData: this.data.pelData.concat(this.data.textObj),
                sendData: this.data.sendData.concat(`绘制文字：${this.data.textObj.x},${this.data.textObj.y},${this.data.textObj.rotation},${this.data.textObj.xRatio},${this.data.textObj.yRatio},${this.data.textObj.text}`),
            })
        } else if (this.data.printIndex === 3) {
            // 绘制条码
            this.setData({
                pelData: this.data.pelData.concat(this.data.barcodeObj),
                sendData: this.data.sendData.concat(`绘制条码：${this.data.barcodeObj.x},${this.data.barcodeObj.y},${this.data.barcodeObj.codeType},${this.data.barcodeObj.height},${this.data.barcodeObj.displayBarCode},${this.data.barcodeObj.rotation},${this.data.barcodeObj.narrow},${this.data.barcodeObj.content}`),
            })
        } else if (this.data.printIndex === 4) {
            // 绘制二维码
            this.setData({
                pelData: this.data.pelData.concat(this.data.qrcodeObj),
                sendData: this.data.sendData.concat(`绘制二维码：${this.data.qrcodeObj.x},${this.data.qrcodeObj.y},${this.data.qrcodeObj.eccLevel},${this.data.qrcodeObj.cellWidth},${this.data.qrcodeObj.encodeMode},${this.data.qrcodeObj.rotation},${this.data.qrcodeObj.codeMode},${this.data.qrcodeObj.mask},${this.data.qrcodeObj.content}`),
            })
        } else if (this.data.printIndex === 5) {
            // 绘制图片
            this.getImageData(() => {
                this.setData({
                    pelData: this.data.pelData.concat(this.data.bitMapObj),
                    sendData: this.data.sendData.concat(`绘制图片：${this.data.bitMapObj.x},${this.data.bitMapObj.y}`),
                })
            })
        } else if (this.data.printIndex === 6) {
            // 绘制压缩图片
            this.getImageData(() => {
                this.setData({
                    pelData: this.data.pelData.concat(this.data.compressBitMapObj),
                    sendData: this.data.sendData.concat(`绘制压缩图片：${this.data.compressBitMapObj.x},${this.data.compressBitMapObj.y},${this.data.compressBitMapObj.dpi}`),
                })
            })

        } else if (this.data.printIndex === 9) {
            // 设置打印偏移
            CTPL.setShiftWithX(this.data.shiftX, this.data.shiftY)
            this.setData({
                sendData: this.data.sendData.concat(`设置标签图像的横纵坐标：${this.data.shiftX},${this.data.shiftY}`),
            })
        } else if (this.data.printIndex === 10) {
            // 设置图像反白
            CTPL.reverseWithX(this.data.reverseX, this.data.reverseY, this.data.reverseWidth, this.data.reverseHeight)
            this.setData({
                sendData: this.data.sendData.concat(`将指定区域的图像缓存反白：${this.data.reverseX},${this.data.reverseY},${this.data.reverseWidth},${this.data.reverseHeight}`),
            })
        }
        this.setData({
            showDialog1: false,
        })
    },
    //清空数据
    clearData() {
        this.setData({
            sendData: [],
            acceptData: [],
            pelData: [],
        })
        CTPL.clearCache()
    },
    // 获取图片信息
    getImageData(callback) {
        const $this = this
        wx.getImageInfo({
            src: this.data.imagePath,
            success: (res) => {
                const scale = ($this.data.paperObj.width * 8) / res.width
                const newWidth = $this.data.paperObj.width * 8
                const newHeight = Math.ceil(res.height * scale)
                const ctx = wx.createCanvasContext("canvas", this)
                ctx.drawImage(this.data.imagePath, 0, 0, newWidth, newHeight)
                ctx.draw(false, () => {
                    wx.canvasGetImageData({
                        canvasId: "canvas",
                        x: 0,
                        y: 0,
                        width: $this.data.paperObj.width * 8,
                        height: newHeight,
                        success: function (response) {
                            $this.data.imageRes = response
                            callback(response)
                        },
                        fail: function (res) {
                            callback(res)
                        },
                    },
                        $this
                    )
                })
            },
        })
    },
    processPrint(pageData, index = 0) {
        const $this = this
        let ctx
        if (!ctx) {
            ctx = wx.createCanvasContext('canvas', $this)
        }
        wx.getImageInfo({
            src: index % 2 === 0 ? this.data.imagePath : this.data.imagePath1,
            success: (res) => {
                ctx.drawImage(index % 2 === 0 ? this.data.imagePath : this.data.imagePath1, 0, 0, res.width, res.height)
                // 延时发送时间
                let delay = this.data.paperObj.width * 1000 / 40
                // 绘制到画布
                ctx.draw(false, () => {
                    wx.canvasGetImageData({
                        canvasId: 'canvas',
                        x: 0,
                        y: 0,
                        width: res.width,
                        height: res.height,
                        success: (res) => {
                            CTPL.setSize(this.data.paperObj.width, this.data.paperObj.height)
                            CTPL.clearCache()
                            CTPL.drawCompressBitMap(0, 0, res.width, res.height, 8, 0, res.data)
                            CTPL.setPrintCopies(1, 1)
                            CTPL.execute((res) => {
                                if (index !== pageData.length - 1 && res) {
                                    setTimeout(() => {
                                        $this.processPrint(pageData, ++index)
                                    }, delay)
                                } else {
                                    wx.hideLoading()
                                }
                            })
                        },
                        fail: (err) => {
                            console.error(err)
                        }
                    })
                })
            }
        })
    },
    //打印
    print() {
        wx.showLoading()
        const $this = this
        CTPL.setSize(this.data.paperObj.width, this.data.paperObj.height)
        CTPL.clearCache()
        this.data.pelData.forEach((value) => {
            if (value.type == "line") {
                CTPL.drawLine(value.x, value.y, value.width, value.height)
            } else if (value.type == "text") {
                CTPL.drawText(
                    value.x,
                    value.y,
                    value.rotation,
                    value.xRatio,
                    value.yRatio,
                    value.text
                )
            } else if (value.type == "barcode") {
                CTPL.drawBarCode(
                    value.x,
                    value.y,
                    value.codeType,
                    value.height,
                    value.displayBarCode,
                    value.rotation,
                    value.narrow,
                    value.content
                )
            } else if (value.type == "qrcode") {
                CTPL.drawQRCode(
                    value.x,
                    value.y,
                    value.eccLevel,
                    value.cellWidth,
                    value.encodeMode,
                    value.codeMode,
                    value.mask,
                    value.content
                )
            } else if (value.type == "bitmap") {
                CTPL.drawBitMap(
                    value.x,
                    value.y,
                    $this.data.imageRes.width,
                    $this.data.imageRes.height,
                    0,
                    $this.data.imageRes.data
                )
            } else if (value.type == "compressbitmap") {
                CTPL.drawCompressBitMap(
                    value.x,
                    value.y,
                    $this.data.imageRes.width,
                    $this.data.imageRes.height,
                    8,
                    0,
                    $this.data.imageRes.data
                )
            }
        })
        CTPL.setPrintCopies(1, 1)
        CTPL.execute()
        wx.hideLoading()
    },
})
