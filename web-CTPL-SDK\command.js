"use strict"; function _instanceof(n, e) { return null != e && "undefined" != typeof Symbol && e[Symbol.hasInstance] ? !!e[Symbol.hasInstance](n) : n instanceof e } Object.defineProperty(exports, "__esModule", { value: true }); exports.default = void 0; var _pako_deflateMin = _interopRequireDefault(require("./pako_deflate.min.js")); function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e } } function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o }, _typeof(o) } function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread() } function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0 } } function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r) } function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r) } function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++)n[e] = r[e]; return n } function _classCallCheck(a, n) { if (!_instanceof(a, n)) throw new TypeError("Cannot call a class as a function") } function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o) } } function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e } function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + "" } function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === r ? String : Number)(t) } var encodeToGb2312 = require("./encodeToGb2312.min.js"); var Command = function () { function Command() { _classCallCheck(this, Command); this.kHead = 0xDD; this.kTail = 0xDD; this.kSerial = 0x00; this.kCommandFactory = 0x04; this.kCommandUser = 0x05; this.KCommandDevelopment = 0x06; this.kCommandPrint = 0x01; this.text = ""; this.buffer = []; this.printBuffer = [] } return _createClass(Command, [{ key: "stringToBuffer", value: function stringToBuffer(a) { var b = this.hexStringToByteArray(encodeToGb2312(a)); return new Uint8Array(b) } }, { key: "hexStringToByteArray", value: function hexStringToByteArray(a) { var b = []; for (var i = 0; i < a.length; i += 2) { var c = parseInt(a.substr(i, 2), 16); b.push(c) } return b } }, { key: "machineModelInfo", value: function machineModelInfo() { var a = [this.kSerial, this.kCommandFactory, 0x02, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "queryCompressPrintCompletion", value: function queryCompressPrintCompletion() { var a = [this.kSerial, this.kCommandUser, 0x35, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "paperTypeInfo", value: function paperTypeInfo() { var a = [this.kSerial, this.kCommandUser, 0x11, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setPaperType", value: function setPaperType(a) { var b = [this.kSerial, this.kCommandUser, 0x11, 0x00, 0x01, a]; var c = this.crc8(b); var d = new Uint8Array([this.kHead].concat(b, [c, this.kTail])).buffer; return d } }, { key: "memoryPrintInfo", value: function memoryPrintInfo() { var a = [this.kSerial, this.kCommandUser, 0x07, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setMemoryPrint", value: function setMemoryPrint(a) { var b = [this.kSerial, this.kCommandUser, 0x07, 0x00, 0x01, a]; var c = this.crc8(b); var d = new Uint8Array([this.kHead].concat(b, [c, this.kTail])).buffer; return d } }, { key: "autoSleepInfo", value: function autoSleepInfo() { var a = [this.kSerial, this.kCommandUser, 0x12, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setAutoSleepTime", value: function setAutoSleepTime(a) { var b = [this.kSerial, this.kCommandUser, 0x12, 0x00, 0x01, a]; var c = this.crc8(b); var d = new Uint8Array([this.kHead].concat(b, [c, this.kTail])).buffer; return d } }, { key: "powerInfo", value: function powerInfo() { var a = [this.kSerial, this.kCommandUser, 0x06, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "hardwareVersionInfo", value: function hardwareVersionInfo() { var a = [this.kSerial, this.kCommandUser, 0x33, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "printModeInfo", value: function printModeInfo() { var a = [this.kSerial, this.kCommandUser, 0x34, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setPrintMode", value: function setPrintMode(a) { var b = [this.kSerial, this.kCommandUser, 0x34, 0x00, 0x01, a]; var c = this.crc8(b); var d = new Uint8Array([this.kHead].concat(b, [c, this.kTail])).buffer; return d } }, { key: "restoreFactorySettingsInfo", value: function restoreFactorySettingsInfo() { var a = [this.kSerial, this.kCommandUser, 0x31, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "speedInfo", value: function speedInfo() { var a = [this.kSerial, this.kCommandUser, 0x03, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setSpeedInfo", value: function setSpeedInfo(a) { var b = "SPEED ".concat(a, "\r\n"); return this.stringToBuffer(b).buffer } }, { key: "densityInfo", value: function densityInfo() { var a = [this.kSerial, this.kCommandUser, 0x02, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setDensity", value: function setDensity(a) { var b = "DENSITY ".concat(a, "\r\n"); return this.stringToBuffer(b).buffer } }, { key: "deviceConfigInfo", value: function deviceConfigInfo() { var a = [this.kSerial, this.KCommandDevelopment, 0x02, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "printStateInfo", value: function printStateInfo() { var a = [this.kSerial, this.kCommandPrint, 0x01, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "setVID", value: function setVID(a, b) { var c = [this.kSerial, this.KCommandDevelopment, 0x08, 0x00, 0x04, (a & 0xFF00) >> 8, a & 0x00FF, (b & 0xFF00) >> 8, b & 0x00FF]; var d = this.crc8(c); var e = new Uint8Array([this.kHead].concat(c, [d, this.kTail])).buffer; return e } }, { key: "firmwareConfigInfo", value: function firmwareConfigInfo() { var a = [this.kSerial, this.KCommandDevelopment, 0x03, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "displayInfo", value: function displayInfo() { var a = [this.kSerial, this.KCommandDevelopment, 0x04, 0x00, 0x00]; var b = this.crc8(a); var c = new Uint8Array([this.kHead].concat(a, [b, this.kTail])).buffer; return c } }, { key: "devicePrintTestPage", value: function devicePrintTestPage() { var a = "SELFTEST\r\n"; return this.stringToBuffer(a).buffer } }, { key: "getBuffer", value: function getBuffer() { var a = _toConsumableArray(this.buffer); return this.concatBuffers(a).buffer } }, { key: "concatBuffers", value: function concatBuffers(a) { var b = 0; for (var i = 0; i < a.length; i++) { b += a[i].byteLength } var c = new Uint8Array(b); var d = 0; for (var e = 0; e < a.length; e++) { c.set(new Uint8Array(a[e]), d); d += a[e].byteLength } return c } }, { key: "setSize", value: function setSize(a, b) { this.buffer = []; var c = "SIZE ".concat(a, " mm, ").concat(b, " mm\r\n"); this.buffer.push(this.stringToBuffer(c)) } }, { key: "setShiftWithX", value: function setShiftWithX(a, b) { var c = "SHIFT ".concat(a, ",").concat(b, "\r\n"); return this.stringToBuffer(c).buffer } }, { key: "reverseWithX", value: function reverseWithX(a, b, c, d) { var e = "REVERSE ".concat(a, ",").concat(b, ",").concat(c, ",").concat(d, "\r\n"); return this.stringToBuffer(e).buffer } }, { key: "setDirection", value: function setDirection(a, b) { var c = "DIRECTION ".concat(a, ",").concat(b, "\r\n"); return this.stringToBuffer(c).buffer } }, { key: "setReference", value: function setReference(a, b) { var c = "REFERENCE ".concat(a, ",").concat(b, "\r\n"); return this.stringToBuffer(c).buffer } }, { key: "addOfflineBitmapWithId", value: function addOfflineBitmapWithId(a, b, c, d, e) { this.buffer = []; var f = "TEMPMAP ".concat(a, ",").concat(b, ",").concat(c, ",\"").concat(d, "\","); this.buffer.push(this.stringToBuffer(f)); if (e) { this.buffer.push(this.convertBitMapImage(e, b * 8, c * 8)) } else { this.buffer.push(new Uint8Array(0x00)) } this.buffer.push(this.stringToBuffer("\r\n")) } }, { key: "clearCache", value: function clearCache() { var a = "CLS\r\n"; this.buffer.push(this.stringToBuffer(a)) } }, { key: "formFeed", value: function formFeed() { this.buffer = []; var a = "FORMFEED\r\n"; this.buffer.push(this.stringToBuffer(a)) } }, { key: "drawLine", value: function drawLine(a, b, c, d) { var e = "BAR ".concat(a, ",").concat(b, ",").concat(c, ",").concat(d, "\r\n"); this.buffer.push(this.stringToBuffer(e)) } }, { key: "drawText", value: function drawText(a, b, c) { var d = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1; var e = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1; var f = arguments.length > 5 ? arguments[5] : undefined; var g = "TEXT ".concat(a, ",").concat(b, ",\"TSS24.BF2\",").concat(c, ",").concat(d, ",").concat(e, ",1,\"").concat(f, "\"\r\n"); this.buffer.push(this.stringToBuffer(g)) } }, { key: "drawBarCode", value: function drawBarCode(a, b, c, d, e, f, g, h) { var i = "BARCODE ".concat(a, ",").concat(b, ",\"").concat(c, "\",").concat(d, ",").concat(e, ",").concat(f, ",").concat(g, ",1,\"").concat(h, "\"\r\n"); this.buffer.push(this.stringToBuffer(i)) } }, { key: "drawQRCode", value: function drawQRCode(a, b, c, d, e, f, g, h) { if (Number(d) !== 0) { var i = "QRCODE ".concat(a, ",").concat(b, ",").concat(c, ",").concat(d, ",").concat(e, ",0,").concat(f, ",").concat(g, ",\"").concat(h, "\"\r\n"); this.buffer.push(this.stringToBuffer(i)) } } }, { key: "drawBitMap", value: function drawBitMap(a, b, c, d, e, f) { var g = "BITMAP ".concat(a, ",").concat(b, ",").concat(c / 8, ",").concat(d, ",").concat(e, ","); this.buffer.push(this.stringToBuffer(g)); this.buffer.push(this.convertBitMapImage(f, c, d)); this.buffer.push(this.stringToBuffer("\r\n")) } }, { key: "drawCompressBitMap", value: function drawCompressBitMap(a, b, c, d) { var e = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 8; var f = arguments.length > 5 ? arguments[5] : undefined; var g = arguments.length > 6 ? arguments[6] : undefined; var h = 10; var j = Math.ceil(d / (h * e)); if (j < 1) { var k = this.convertBitMapImage(g, c, d, false); var l = new _pako_deflateMin.default.Deflate({ level: -1, method: 8, windowBits: -15, memLevel: 9, strategy: 0 }); l.push(k, true); var m = l.result; var n = "BITMAP ".concat(a, ",").concat(b, ",").concat(c / 8, ",").concat(d, ",3,").concat(m.length, ","); this.buffer.push(this.stringToBuffer(n)); this.buffer.push(new Uint8Array(m)); this.buffer.push(this.stringToBuffer("\r\n")) } else { var o = this.convertBitMapImage(g, c, d, false); var p = c * h * (e / 8.0); var q = o.length % p; var r = Math.floor(o.length / p) + 1; for (var i = 0; i < r; i++) { if (i < Math.floor(o.length / p)) { var s = o.slice(i * p, i * p + p); var t = new _pako_deflateMin.default.Deflate({ level: -1, method: 8, windowBits: -15, memLevel: 9, strategy: 0 }); t.push(s, true); var u = t.result; var n = "BITMAP ".concat(a, ",").concat(b + i * (e * h), ",").concat(c / 8, ",").concat(h * e, ",3,").concat(u.length, ","); this.buffer.push(this.stringToBuffer(n)); this.buffer.push(new Uint8Array(u)); this.buffer.push(this.stringToBuffer("\r\n")) } else { var s = o.slice(i * p, i * p + q); if (s.length > 0) { var v = new _pako_deflateMin.default.Deflate({ level: -1, method: 8, windowBits: -15, memLevel: 9, strategy: 0 }); v.push(s, true); var w = v.result; var x = (d / e - i * h) * e; var n = "BITMAP ".concat(a, ",").concat(b + i * (e * h), ",").concat(c / 8, ",").concat(x, ",3,").concat(w.length, ","); this.buffer.push(this.stringToBuffer(n)); this.buffer.push(new Uint8Array(w)); this.buffer.push(this.stringToBuffer("\r\n")) } } } } } }, { key: "drawLongCompressBitMap", value: function drawLongCompressBitMap(a, b, c, d) { var e = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 8; var f = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 4; var g = arguments.length > 6 ? arguments[6] : undefined; var h = 10; var j = Math.ceil(d / 1.0 / (h * e) + 0.55555); if (j < 1) { var k = this.convertBitMapImage(g, c, d, false); var l = new _pako_deflateMin.default.Deflate({ level: -1, method: 8, windowBits: -15, memLevel: 9, strategy: 0 }); l.push(k, true); var m = l.result; var n = "BITMAP ".concat(a, ",").concat(b, ",").concat(c / 8, ",").concat(d, ",").concat(f, ",").concat(m.length, ","); this.buffer.push(this.stringToBuffer(n)); this.buffer.push(new Uint8Array(m)); this.buffer.push(this.stringToBuffer("\r\n")) } else { var o = this.convertBitMapImage(g, c, d, false); var p = c * h * (e / 8.0); var q = o.length % p; var r = Math.floor(o.length / p) + 1; for (var i = 0; i < r; i++) { if (i < Math.floor(o.length / p)) { var s = o.slice(i * p, i * p + p); var t = new _pako_deflateMin.default.Deflate({ level: -1, method: 8, windowBits: -15, memLevel: 9, strategy: 0 }); t.push(s, true); var u = t.result; var n = "BITMAP ".concat(a, ",").concat(b + i * (e * h), ",").concat(c / 8, ",").concat(h * e, ",").concat(f, ",").concat(u.length, ","); this.buffer.push(this.stringToBuffer(n)); this.buffer.push(new Uint8Array(u)); this.buffer.push(this.stringToBuffer("\r\n")) } else { var s = o.slice(i * p, i * p + q); if (s.length > 0) { var v = new _pako_deflateMin.default.Deflate({ level: -1, method: 8, windowBits: -15, memLevel: 9, strategy: 0 }); v.push(s, true); var w = v.result; var x = (d / e - i * h) * e; var n = "BITMAP ".concat(a, ",").concat(b + i * (e * h), ",").concat(c / 8, ",").concat(x, ",").concat(f, ",").concat(w.length, ","); this.buffer.push(this.stringToBuffer(n)); this.buffer.push(new Uint8Array(w)); this.buffer.push(this.stringToBuffer("\r\n")) } } } } } }, { key: "setPrintCopies", value: function setPrintCopies(a, b) { var c = "PRINT ".concat(a, ",").concat(b, "\r\n"); this.buffer.push(this.stringToBuffer(c)) } }, { key: "convertBitMapImage", value: function convertBitMapImage(a, b, c) { var d = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true; var e = []; var f = 1; var g = 0; if (d) { f = 0; g = 1 } for (var i = 0; i < a.length; i += 4) { var h = a[i]; var k = a[i + 1]; var l = a[i + 2]; var m = 0.2989 * h + 0.587 * k + 0.114 * l; var n = 128; var o = m < n ? f : g; e.push(o) } var p = new Uint8Array(b / 8 * c); for (var q = 0; q < e.length / 8; q++) { var r = []; for (var j = 0; j < 8; j++) { var s = e[q * 8 + j]; r.push(s) } p[q] = r[0] * 128 + r[1] * 64 + r[2] * 32 + r[3] * 16 + r[4] * 8 + r[5] * 4 + r[6] * 2 + r[7] } return new Uint8Array(p) } }, { key: "crc8", value: function crc8(a) { var b = [0x00, 0x07, 0x0E, 0x09, 0x1C, 0x1B, 0x12, 0x15, 0x38, 0x3F, 0x36, 0x31, 0x24, 0x23, 0x2A, 0x2D, 0x70, 0x77, 0x7E, 0x79, 0x6C, 0x6B, 0x62, 0x65, 0x48, 0x4F, 0x46, 0x41, 0x54, 0x53, 0x5A, 0x5D, 0xE0, 0xE7, 0xEE, 0xE9, 0xFC, 0xFB, 0xF2, 0xF5, 0xD8, 0xDF, 0xD6, 0xD1, 0xC4, 0xC3, 0xCA, 0xCD, 0x90, 0x97, 0x9E, 0x99, 0x8C, 0x8B, 0x82, 0x85, 0xA8, 0xAF, 0xA6, 0xA1, 0xB4, 0xB3, 0xBA, 0xBD, 0xC7, 0xC0, 0xC9, 0xCE, 0xDB, 0xDC, 0xD5, 0xD2, 0xFF, 0xF8, 0xF1, 0xF6, 0xE3, 0xE4, 0xED, 0xEA, 0xB7, 0xB0, 0xB9, 0xBE, 0xAB, 0xAC, 0xA5, 0xA2, 0x8F, 0x88, 0x81, 0x86, 0x93, 0x94, 0x9D, 0x9A, 0x27, 0x20, 0x29, 0x2E, 0x3B, 0x3C, 0x35, 0x32, 0x1F, 0x18, 0x11, 0x16, 0x03, 0x04, 0x0D, 0x0A, 0x57, 0x50, 0x59, 0x5E, 0x4B, 0x4C, 0x45, 0x42, 0x6F, 0x68, 0x61, 0x66, 0x73, 0x74, 0x7D, 0x7A, 0x89, 0x8E, 0x87, 0x80, 0x95, 0x92, 0x9B, 0x9C, 0xB1, 0xB6, 0xBF, 0xB8, 0xAD, 0xAA, 0xA3, 0xA4, 0xF9, 0xFE, 0xF7, 0xF0, 0xE5, 0xE2, 0xEB, 0xEC, 0xC1, 0xC6, 0xCF, 0xC8, 0xDD, 0xDA, 0xD3, 0xD4, 0x69, 0x6E, 0x67, 0x60, 0x75, 0x72, 0x7B, 0x7C, 0x51, 0x56, 0x5F, 0x58, 0x4D, 0x4A, 0x43, 0x44, 0x19, 0x1E, 0x17, 0x10, 0x05, 0x02, 0x0B, 0x0C, 0x21, 0x26, 0x2F, 0x28, 0x3D, 0x3A, 0x33, 0x34, 0x4E, 0x49, 0x40, 0x47, 0x52, 0x55, 0x5C, 0x5B, 0x76, 0x71, 0x78, 0x7F, 0x6A, 0x6D, 0x64, 0x63, 0x3E, 0x39, 0x30, 0x37, 0x22, 0x25, 0x2C, 0x2B, 0x06, 0x01, 0x08, 0x0F, 0x1A, 0x1D, 0x14, 0x13, 0xAE, 0xA9, 0xA0, 0xA7, 0xB2, 0xB5, 0xBC, 0xBB, 0x96, 0x91, 0x98, 0x9F, 0x8A, 0x8D, 0x84, 0x83, 0xDE, 0xD9, 0xD0, 0xD7, 0xC2, 0xC5, 0xCC, 0xCB, 0xE6, 0xE1, 0xE8, 0xEF, 0xFA, 0xFD, 0xF4, 0xF3]; var c = 0x00; for (var i = 0; i < a.length; i++) { var d = c ^ a[i]; c = b[d] } return c } }]) }(); var _default = exports.default = new Command()