"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.hardwareFeatures=exports.firmwareFeatures=exports.displayFeatures=void 0;var hardwareFeatures=exports.hardwareFeatures=[{infoId:0x01,infoLen:16,infoKey:"Model"},{infoId:0x02,infoLen:1,infoKey:"Dpi"},{infoId:0x03,infoLen:1,infoKey:"SupportCompress"},{infoId:0x04,infoLen:1,infoKey:"TwoColor"},{infoId:0x05,infoLen:1,infoKey:"DeviceCutter"},{infoId:0x06,infoLen:1,infoKey:"SupportCarbonDetection"},{infoId:0x07,infoLen:1,infoKey:"DevicePrintMaxWidthMM"},{infoId:0x08,infoLen:1,infoKey:"DevicePrintMaxHeightMM"},{infoId:0x09,infoLen:1,infoKey:"PrintAlignment"}];var firmwareFeatures=exports.firmwareFeatures=[{infoId:0x01,infoLen:4,infoKey:"State"},{infoId:0x02,infoLen:1,infoKey:"PaperType"},{infoId:0x03,infoLen:1,infoKey:"GapHeightMM"},{infoId:0x04,infoLen:1,infoKey:"FeedOffsetMM"},{infoId:0x05,infoLen:1,infoKey:"Density"},{infoId:0x06,infoLen:2,infoKey:"Speed"},{infoId:0x07,infoLen:1,infoKey:"Direction"},{infoId:0x08,infoLen:1,infoKey:"PrintOffsetYMM"},{infoId:0x09,infoLen:1,infoKey:"PrintOffsetXMM"},{infoId:0x0A,infoLen:1,infoKey:"CalibrationMM"},{infoId:0x0B,infoLen:1,infoKey:"AutoShutdownMinute"},{infoId:0x0C,infoLen:4,infoKey:"MemoryIdle"}];var displayFeatures=exports.displayFeatures=[{infoId:0x01,infoLen:1,infoKey:"PrintMode"},{infoId:0x02,infoLen:48,infoKey:"SerialNumber"},{infoId:0x03,infoLen:16,infoKey:"FirmwareVersion"},{infoId:0x04,infoLen:6,infoKey:"HardwareVersion"},{infoId:0x05,infoLen:1,infoKey:"Battery"}];