/*
 * @Author: ji<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-02 09:33:36
 * @LastEditors: jianfanfan <EMAIL>
 * @LastEditTime: 2024-01-07 10:28:57
 * @FilePath: \mini-program-websdk\pages\connect\connect.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// pages/connect.js
import CTPL from "../../web-CTPL-SDK/ctpl.js";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    blueDeviceList: [], //蓝牙设备列表
    blueName: "", //蓝牙设备名称
    blueDeviceId: "", //蓝牙设备特征值
    blueIndex: -1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  async onReady() {
    await CTPL.init();
    this.discoveryList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  clickLeft() {
    wx.navigateBack();
  },
  async discoveryList() {
    const $this = this;
    CTPL.discovery(function (res) {
      $this.setData({
        blueDeviceList: $this.data.blueDeviceList.concat(res),
      });
    })
    wx.hideLoading();
  },
  //重新扫描
  rescan() {
    this.setData({
      blueDeviceList: [],
    });
    this.discoveryList();
  },
  //开始连接蓝牙
  connect(event) {
    const $this = this;
    const { device, index } = event.currentTarget.dataset;
    CTPL.connect(
      device,
      function success(res) {
        $this.setData({
          blueName: device.name,
          blueDeviceId: device.deviceId,
          blueIndex: index
        });
        wx.navigateTo({
          url: `/pages/index/index?blename=${device.name}&deviceId=${device.deviceId}`,
        });
      },
      function fail(err) {
        console.log('err', err)
        wx.showToast({ title: '连接失败' })
      }
    );
  },
})