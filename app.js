/*
 * @Author: ji<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-12-29 09:18:41
 * @LastEditors: jianfanfan <EMAIL>
 * @LastEditTime: 2024-01-03 11:49:48
 * @FilePath: \mini-program-websdk\app.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null,
    eventBus: new Map(),
  }
})
