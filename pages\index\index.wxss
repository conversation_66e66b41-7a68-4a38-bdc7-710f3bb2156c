/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  /* padding-top: 80px; */
}

.title {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
  padding: 0 8px;
  font-size: 14px;
}

.title .namedevice {
 display:flex;
 flex-direction: column;
}

.title .blueconnect {
  color: #0497FF;
}

.data-title {
  position: relative;
  font-size: 14px;
  padding-left: 20px;
  margin: 5px 0;
}

.data-title::after {
  position: absolute;
  content: "";
  width: 2px;
  height: 80%;
  background: #0497FF;
  left: 10px;
  top: 10%;
}

.scrollarea {
  background: #efefef;
  height: 200px;
  font-size: 14px;
}

.scrollarea .item {
  width: 100%;
}

.btn {
  display: flex;
  justify-content: flex-end;
}

.btn button {
  background: #0497FF;
  font-size: 14px;
  color: #fff;
  width: 80px;
  height: 36px;
  margin: 10px 10px 30px 0;
}

.instruct .title {
  font-weight: bold;
  padding-left: 16px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #efefef;
  position: relative;
  font-size: 14px;
}

.instruct .title::after {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  right: 20px;
  top: 14px;
  transform: rotate(45deg);
}

.instruct .text {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  padding-left: 16px;
}

.instruct .text.active {
  background: #efefef;
}

.custom-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.dialog-content {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
}

.close-btn {
  margin-top: 10px;
  text-align: center;
  color: #333;
  cursor: pointer;
}

.popup-cont {
  display: block;
  width: 80%;
  background: #fff;
}

.popup-cont .popup-list {
  border: 1px solid #ebeef5;
  border-bottom: none;
  border-radius: 10rpx;
}


.popup-cont .popup-list input {
  height: 60rpx;
  font-size: 24rpx;
  border-bottom: 1px solid #ebeef5;
  padding-left: 15rpx;
}

.dialog-btn {
  display: flex;
  width: 80%;
  background: #fff;
}

.dialog-btn text {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-size: 14px;
}

.dialog-btn .confirm {
  color: #0497FF;
}

#canvas {
  position: absolute;
  top: -9999px;
}