"use strict";function _instanceof(n,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](n):n instanceof e}Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;function _typeof(o){"@babel/helpers - typeof";return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},_typeof(o)}function _toConsumableArray(r){return _arrayWithoutHoles(r)||_iterableToArray(r)||_unsupportedIterableToArray(r)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}function _unsupportedIterableToArray(r,a){if(r){if("string"==typeof r)return _arrayLikeToArray(r,a);var t={}.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(r,a):void 0}}function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}function _arrayWithoutHoles(r){if(Array.isArray(r))return _arrayLikeToArray(r)}function _arrayLikeToArray(r,a){(null==a||a>r.length)&&(a=r.length);for(var e=0,n=Array(a);e<a;e++)n[e]=r[e];return n}function _classCallCheck(a,n){if(!_instanceof(a,n))throw new TypeError("Cannot call a class as a function");}function _defineProperties(e,r){for(var t=0;t<r.length;t++){var o=r[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(t){var i=_toPrimitive(t,"string");return"symbol"==_typeof(i)?i:i+""}function _toPrimitive(t,r){if("object"!=_typeof(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return("string"===r?String:Number)(t)}var encodeToGb2312=require("./encodeToGb2312.min.js");var ESCCommand=function(){function ESCCommand(){_classCallCheck(this,ESCCommand);this.escBuffer=[]}return _createClass(ESCCommand,[{key:"initPrinter",value:function initPrinter(){this.escBuffer=[];var a=[0x1B,0x40];this.escBuffer.push(new Uint8Array(a))}},{key:"setAlignment",value:function setAlignment(a){var b=[0x1B,0x61,a];this.escBuffer.push(new Uint8Array(b))}},{key:"setFontSize",value:function setFontSize(a,b){var c=[0x1D,0x21,a*16+b];this.escBuffer.push(new Uint8Array(c))}},{key:"drawTextContent",value:function drawTextContent(a){var b=this.stringToBuffer(a);this.escBuffer.push(b);this.escBuffer.push(new Uint8Array([0x0A]))}},{key:"setTextPosition",value:function setTextPosition(a){var b=[0x1D,0x48,a];this.escBuffer.push(new Uint8Array(b))}},{key:"setBarCodeSize",value:function setBarCodeSize(a,b){var c=[0x1D,0x77,a,0x1D,0x68,b];this.escBuffer.push(new Uint8Array(c))}},{key:"drawBarcode128WithValue",value:function drawBarcode128WithValue(a){var b=[0x1D,0x6B,73,a.length+2,0x7B,0x41];var c=this.stringToBuffer(a);this.escBuffer.push(new Uint8Array(b),new Uint8Array(c))}},{key:"setQRCodeWidth",value:function setQRCodeWidth(a){var b=[0x1D,0x28,0x6B,0x03,0x00,0x31,0x43,a];this.escBuffer.push(new Uint8Array(b))}},{key:"drawQRCode",value:function drawQRCode(a){var k=a.length+3;var b=k/256;var c=k%256;var d=[0x1D,0x28,0x6B,c,b,0x31,0x50,0x30];var e=this.stringToBuffer(a);var f=[0x1D,0x28,0x6B,0x03,0x00,0x31,0x51,0x30];this.escBuffer.push(new Uint8Array(d),new Uint8Array(e),new Uint8Array(f))}},{key:"drawLine",value:function drawLine(a,b){var c=[0x1D,0x28,0x52,a/256,a%256,b/256,b%256];this.escBuffer.push(new Uint8Array(c))}},{key:"drawBitMap",value:function drawBitMap(a,b,c){var d=this.escConvertBitMapImage(a,b,c);this.escBuffer.push(new Uint8Array(d))}},{key:"submitPrint",value:function submitPrint(){this.escBuffer.push(new Uint8Array([0x0A]))}},{key:"stringToBuffer",value:function stringToBuffer(a){var b=this.hexStringToByteArray(encodeToGb2312(a));return new Uint8Array(b)}},{key:"hexStringToByteArray",value:function hexStringToByteArray(a){var b=[];for(var i=0;i<a.length;i+=2){var c=parseInt(a.substr(i,2),16);b.push(c)}return b}},{key:"escConvertBitMapImage",value:function escConvertBitMapImage(a,b,c){var d=[];var w=b;var h=c;var e=parseInt((w+7)/8)*8;var f=h;var g=parseInt(e/8);var j=new Uint8Array(f*g);d.push(29);d.push(118);d.push(48);d.push(0);d.push(parseInt(g%256));d.push(parseInt(g/256));d.push(parseInt(f%256));d.push(parseInt(f/256));for(var y=0;y<h;y++){for(var x=0;x<w;x++){var k=a[(y*w+x)*4];if(k<128){j[parseInt(y*g+x/8)]|=0x80>>x%8}}}for(var i=0;i<j.length;i++){d.push(j[i])}return d}},{key:"getBuffer",value:function getBuffer(){var a=_toConsumableArray(this.escBuffer);return this.concatBuffers(a).buffer}},{key:"concatBuffers",value:function concatBuffers(a){var b=0;for(var i=0;i<a.length;i++){b+=a[i].byteLength}var c=new Uint8Array(b);var d=0;for(var e=0;e<a.length;e++){c.set(new Uint8Array(a[e]),d);d+=a[e].byteLength}return c}}])}();var _default=exports.default=new ESCCommand();