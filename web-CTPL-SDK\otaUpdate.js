"use strict";function _instanceof(n,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](n):n instanceof e}Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _command=_interopRequireDefault(require("./command"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _typeof(o){"@babel/helpers - typeof";return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},_typeof(o)}function _toConsumableArray(r){return _arrayWithoutHoles(r)||_iterableToArray(r)||_unsupportedIterableToArray(r)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}function _unsupportedIterableToArray(r,a){if(r){if("string"==typeof r)return _arrayLikeToArray(r,a);var t={}.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(r,a):void 0}}function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}function _arrayWithoutHoles(r){if(Array.isArray(r))return _arrayLikeToArray(r)}function _arrayLikeToArray(r,a){(null==a||a>r.length)&&(a=r.length);for(var e=0,n=Array(a);e<a;e++)n[e]=r[e];return n}function _classCallCheck(a,n){if(!_instanceof(a,n))throw new TypeError("Cannot call a class as a function");}function _defineProperties(e,r){for(var t=0;t<r.length;t++){var o=r[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(t){var i=_toPrimitive(t,"string");return"symbol"==_typeof(i)?i:i+""}function _toPrimitive(t,r){if("object"!=_typeof(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return("string"===r?String:Number)(t)}var OTAUpdate=function(){function OTAUpdate(){_classCallCheck(this,OTAUpdate);this.otaBuffer=[];this.otaPacketIndex=0}return _createClass(OTAUpdate,[{key:"requestFirmwareUpdate",value:function requestFirmwareUpdate(){var a=[0x00,0x02,0x02,0x00,0x01,0x01];var b=_command.default.crc8(a);var c=new Uint8Array([0xDD].concat(a,[b,0xDD])).buffer;return c}},{key:"sliceData",value:function sliceData(a,b){var c=Math.min(a,b.length-this.otaPacketIndex);var d=b.slice(this.otaPacketIndex,this.otaPacketIndex+c);var e=Array.from(d);console.log("via: ",e[e.length-1]);this.otaPacketIndex+=c;var f=(d.length&0xFF00)>>8;var g=d.length&0x00FF;var h=[0x00,0x02,0x03,f,g];var i=new Uint8Array([].concat(h,_toConsumableArray(d)));var j=_command.default.crc8(i);var k=new Uint8Array([0xDD].concat(h,_toConsumableArray(d),[j,0xDD]));return k}},{key:"executeFirmwareUpdate",value:function executeFirmwareUpdate(a){var b=_command.default.crc8(a);var c=[0x00,0x02,0x04,0x00,0x01,b];var d=_command.default.crc8(c);var e=new Uint8Array([0xDD].concat(c,[d,0xDD])).buffer;return e}}])}();var _default=exports.default=new OTAUpdate();