/* pages/connect.wxss */

page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.title {
  display: flex;
  justify-content: space-between;
  height: 40px;
  align-items: center;
  padding:0 8px;
  font-size:14px;
  border-bottom: 1px solid #333;
}

.title .blueconnect {
  color: #0497FF;
}

.device-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 110rpx;
  padding-left: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.device-item.active {
  background: #efefef;
}
