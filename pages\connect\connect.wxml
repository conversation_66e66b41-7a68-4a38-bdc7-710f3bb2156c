<!--pages/connect.wxml-->
<view class="connect">
	<view class="title">
		<text bindtap="clickLeft">返回</text>
		<text bindtap="rescan" class="blueconnect">重新扫描</text>
	</view>
	<scroll-view class="scrollarea" scroll-y type="list">
		<view class="device-list">
			<view
			 class="device-item"
			 wx:for="{{blueDeviceList}}"
			 wx:key="*this"
			 bindtap="connect"
			 data-device="{{item}}"
			 data-index="{{index}}"
			 class="{{blueIndex === index ? 'device-item active' : 'device-item'}}"
			>
				<text>{{item.name}}</text>
				<text>{{item.deviceId}}</text>
			</view>
		</view>
	</scroll-view>
</view>
