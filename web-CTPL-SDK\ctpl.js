/*
 * @Author: ji<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-05 17:02:38
 * @LastEditors: jianfanfan <EMAIL>
 * @LastEditTime: 2024-07-30 16:14:38
 * @FilePath: \mini-program-websdk\web-CTPL-SDK\ctpl.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
"use strict"; function _instanceof(n, e) { return null != e && "undefined" != typeof Symbol && e[Symbol.hasInstance] ? !!e[Symbol.hasInstance](n) : n instanceof e } Object.defineProperty(exports, "__esModule", { value: true }); exports.default = void 0; var _command = _interopRequireDefault(require("./command")); var _esccommand = _interopRequireDefault(require("./esccommand")); var _otaUpdate = _interopRequireDefault(require("./otaUpdate")); var _analysisFeature = require("./analysisFeature"); function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e } } function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o }, _typeof(o) } function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread() } function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r) } function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r) } function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var b = 0, F = function F() { }; return { s: F, n: function n() { return b >= r.length ? { done: !0 } : { done: !1, value: r[b++] } }, e: function e(r) { throw r }, f: F } } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } var o, a = !0, u = !1; return { s: function s() { t = t.call(r) }, n: function n() { var r = t.next(); return a = r.done, r }, e: function e(r) { u = !0, o = r }, f: function f() { try { a || null == t.return || t.return() } finally { if (u) throw o } } } } function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0 } } function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++)n[e] = r[e]; return n } function _classCallCheck(a, n) { if (!_instanceof(a, n)) throw new TypeError("Cannot call a class as a function") } function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o) } } function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e } function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + "" } function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value.") } return ("string" === r ? String : Number)(t) } var encodeToGb2312 = require("./encodeToGb2312.min.js"); var CTPL = function () { function CTPL() { _classCallCheck(this, CTPL); this.blueDeviceList = []; this.deviceId = ''; this.serviceId = ''; this.characteristicId = ''; this.writeId = ''; this.notifyId = ''; this.androidMTU = 23; this.iOSMTU = 100; this.platform = 'android'; this.uniPlatform = 'app'; this.otaPacketIndex = 0; this.receiveData = []; if (!CTPL.instance) { CTPL.instance = this } return CTPL.instance } return _createClass(CTPL, [{ key: "init", value: function init() { var d = this; this.blueDeviceList = []; return new Promise(function (b, c) { wx.openBluetoothAdapter({ success: function success(a) { b(a) }, fail: function fail(a) { c(a) }, complete: function complete() { wx.getSystemInfo({ success: function success(a) { d.platform = a.platform } }) } }) }) } }, { key: "stopScanDevice", value: function stopScanDevice() { wx.stopBluetoothDevicesDiscovery() } }, { key: "connect", value: function connect(b, c, d) { var e = this; this.deviceId = b.deviceId; wx.createBLEConnection({ deviceId: e.deviceId, success: function success(a) { e.stopScanDevice(); if (e.platform === 'android') { wx.setBLEMTU({ deviceId: e.deviceId, mtu: 100, complete: function complete() { setTimeout(function () { e.getServices(c, d) }) } }) } else { setTimeout(function () { e.getServices(c, d) }) } }, fail: function fail(a) { d(a) } }) } }, { key: "disconnect", value: function disconnect() { var d = this; return new Promise(function (b, c) { wx.closeBLEConnection({ deviceId: d.deviceId, success: function success(a) { b(a) }, fail: function fail(a) { c(a) } }) }) } }, { key: "getServices", value: function getServices(d, e) { var f = this; wx.getBLEDeviceServices({ deviceId: f.deviceId, success: function success(b) { if (b.services.length !== 0) { var c = b.services.findIndex(function (a) { return a.uuid.includes('49535343') }); if (c !== -1) { f.serviceId = b.services[c].uuid } else { f.serviceId = b.services[0].uuid } f.getCharacteristics(d, e) } else { f.getServices(d, e) } }, fail: function fail(a) { e(a) } }) } }, { key: "getCharacteristics", value: function getCharacteristics(c, d) { var e = this; wx.getBLEDeviceCharacteristics({ deviceId: e.deviceId, serviceId: e.serviceId, success: function success(b) { e.writeId = b.characteristics.filter(function (a) { return a.properties.write && !a.properties.notify })[0].uuid; e.characteristicId = e.writeId; e.notifyId = b.characteristics.filter(function (a) { return a.properties.notify && !a.properties.write })[0].uuid; e.notify(c, d) }, fail: function fail(a) { d(a) } }) } }, { key: "discovery", value: function discovery(i) { var j = this; j.blueDeviceList = []; wx.startBluetoothDevicesDiscovery({ success: function success(h) { wx.onBluetoothDeviceFound(function (g) { wx.getBluetoothDevices({ success: function success(c) { var d = c["devices"]; var e = _createForOfIteratorHelper(d), _step; try { var f = function f() { var b = _step.value; if (b.name.length > 0 && j.blueDeviceList.findIndex(function (a) { return a.name === b.name }) === -1) { j.blueDeviceList.push(b); i(b) } }; for (e.s(); !(_step = e.n()).done;) { f() } } catch (err) { e.e(err) } finally { e.f() } } }) }) }, fail: function fail(a) { console.log('err', a) } }) } }, { key: "notify", value: function notify(b, c) { var d = this; wx.notifyBLECharacteristicValueChange({ state: true, deviceId: d.deviceId, serviceId: d.serviceId, characteristicId: d.notifyId, success: function success(a) { b(a); d.listenValueChange() }, fail: function fail(a) { c(a) } }) } }, { key: "writeBLE", value: function writeBLE(b) { var c = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0; var d = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () { }; var e = this; var f = this.platform === 'android' ? e.androidMTU - 3 : e.iOSMTU; var g = Math.ceil(b.byteLength / f); if (c >= g) { if (d) { d(true) } return } var h = c * f; var i = h + f; var j = b.slice(h, i); wx.writeBLECharacteristicValue({ deviceId: e.deviceId, serviceId: e.serviceId, characteristicId: e.writeId, value: j, success: function success(a) { setTimeout(function () { e.writeBLE(b, c + 1, d) }, 30) }, fail: function fail(a) { console.error('Failed to send package:', a) } }) } }, { key: "listenValueChange", value: function listenValueChange() { var f = this; var g = this; return new Promise(function (d, e) { wx.onBLECharacteristicValueChange(function (a) { var b; var c = new Uint8Array(a.value); (b = f.receiveData).push.apply(b, _toConsumableArray(c)); if (f.receiveData[f.receiveData.length - 1] == 0xdd || f.receiveData[f.receiveData.length - 1] == 0) { d(g.analysisWithData(f.receiveData)); f.receiveData = [] } }) }) } }, { key: "execute", value: function execute(a) { this.writeBLE(_command.default.getBuffer(), 0, a) } }, { key: "sdkVersion", value: function sdkVersion() { return '1.1.0' } }, { key: "queryHardwareModel", value: function queryHardwareModel() { this.writeBLE(_command.default.machineModelInfo()); return this.listenValueChange() } }, { key: "queryCompressPrintCompletion", value: function queryCompressPrintCompletion() { this.writeBLE(_command.default.queryCompressPrintCompletion()); return this.listenValueChange() } }, { key: "queryPaperType", value: function queryPaperType() { this.writeBLE(_command.default.paperTypeInfo()); return this.listenValueChange() } }, { key: "queryMemoryPrint", value: function queryMemoryPrint() { this.writeBLE(_command.default.memoryPrintInfo()); return this.listenValueChange() } }, { key: "queryDensity", value: function queryDensity() { this.writeBLE(_command.default.densityInfo()); return this.listenValueChange() } }, { key: "querySpeed", value: function querySpeed() { this.writeBLE(_command.default.speedInfo()); return this.listenValueChange() } }, { key: "queryAutoShutDown", value: function queryAutoShutDown() { this.writeBLE(_command.default.autoSleepInfo()); return this.listenValueChange() } }, { key: "queryBattery", value: function queryBattery() { this.writeBLE(_command.default.powerInfo()); return this.listenValueChange() } }, { key: "queryHardwareVersion", value: function queryHardwareVersion() { this.writeBLE(_command.default.hardwareVersionInfo()); return this.listenValueChange() } }, { key: "queryPrintMode", value: function queryPrintMode() { this.writeBLE(_command.default.printModeInfo()); return this.listenValueChange() } }, { key: "queryHardwareConfig", value: function queryHardwareConfig() { this.writeBLE(_command.default.deviceConfigInfo()); return this.listenValueChange() } }, { key: "queryFirmwareInfo", value: function queryFirmwareInfo() { this.writeBLE(_command.default.firmwareConfigInfo()); return this.listenValueChange() } }, { key: "queryDisplayInfo", value: function queryDisplayInfo() { this.writeBLE(_command.default.displayInfo()); return this.listenValueChange() } }, { key: "printStateInfo", value: function printStateInfo() { this.writeBLE(_command.default.printStateInfo()); return this.listenValueChange() } }, { key: "formFeed", value: function formFeed() { _command.default.formFeed(); this.writeBLE(_command.default.getBuffer()) } }, { key: "clearCache", value: function clearCache() { _command.default.clearCache() } }, { key: "setAutoShutdown", value: function setAutoShutdown(a) { this.writeBLE(_command.default.setAutoSleepTime(a)) } }, { key: "setPrintMode", value: function setPrintMode(a) { this.writeBLE(_command.default.setPrintMode(a)) } }, { key: "setMemoryPrint", value: function setMemoryPrint(a) { this.writeBLE(_command.default.setMemoryPrint(a)) } }, { key: "setPaperType", value: function setPaperType(a) { this.writeBLE(_command.default.setPaperType(a)) } }, { key: "resetFirmware", value: function resetFirmware() { this.writeBLE(_command.default.restoreFactorySettingsInfo()) } }, { key: "deviceSelfTestPage", value: function deviceSelfTestPage() { this.writeBLE(_command.default.devicePrintTestPage()); return this.listenValueChange() } }, { key: "setDirection", value: function setDirection(a, b) { this.writeBLE(_command.default.setDirection(a, b)) } }, { key: "setReference", value: function setReference(a, b) { this.writeBLE(_command.default.setReference(a, b)) } }, { key: "setDensity", value: function setDensity(a) { this.writeBLE(_command.default.setDensity(a)) } }, { key: "setSpeed", value: function setSpeed(a) { this.writeBLE(_command.default.setSpeedInfo(a)) } }, { key: "setSize", value: function setSize(a, b) { _command.default.setSize(a, b) } }, { key: "setPrintCopies", value: function setPrintCopies(a, b) { _command.default.setPrintCopies(a, b) } }, { key: "setVID", value: function setVID(a, b) { _command.default.setVID(a, b) } }, { key: "setShiftWithX", value: function setShiftWithX(a, b) { this.writeBLE(_command.default.setShiftWithX(a, b)) } }, { key: "reverseWithX", value: function reverseWithX(a, b, c, d) { this.writeBLE(_command.default.reverseWithX(a, b, c, d)) } }, { key: "drawLine", value: function drawLine(a, b, c, d) { _command.default.drawLine(a, b, c, d) } }, { key: "drawText", value: function drawText(a, b, c, d, e, f) { _command.default.drawText(+a, +b, c, d, e, f) } }, { key: "drawBarCode", value: function drawBarCode(a, b, c, d, e, f, g, h) { _command.default.drawBarCode(a, b, c, d, e, f, g, h) } }, { key: "drawQRCode", value: function drawQRCode(a, b, c, d, e, f, g, h) { _command.default.drawQRCode(+a, +b, c, +d, e, f, g, h) } }, { key: "drawBitMap", value: function drawBitMap(a, b, c, d, e, f) { _command.default.drawBitMap(a, b, c, d, e, f) } }, { key: "drawCompressBitMap", value: function drawCompressBitMap(a, b, c, d, e, f, g) { _command.default.drawCompressBitMap(+a, +b, c, d, e, f, g) } }, { key: "drawLongCompressBitMap", value: function drawLongCompressBitMap(a, b, c, d, e, f, g) { _command.default.drawLongCompressBitMap(a, b, c, d, e, f, g) } }, { key: "addOfflineBitmapWithId", value: function addOfflineBitmapWithId(a, b, c, d, e) { _command.default.addOfflineBitmapWithId(a, b, c, d, e) } }, { key: "escInitPrinter", value: function escInitPrinter() { _esccommand.default.initPrinter() } }, { key: "escDrawText", value: function escDrawText(a, b, c, d) { _esccommand.default.setAlignment(a); _esccommand.default.setFontSize(b, c); _esccommand.default.drawTextContent(d) } }, { key: "escDrawLine", value: function escDrawLine(a, b) { _esccommand.default.drawLine(a, b) } }, { key: "escDrawBarCode", value: function escDrawBarCode(a, b, c, d) { _esccommand.default.setTextPosition(a); _esccommand.default.setBarCodeSize(b, c); _esccommand.default.drawBarcode128WithValue(d) } }, { key: "escDrawQRCode", value: function escDrawQRCode(a, b) { _esccommand.default.setQRCodeWidth(a); _esccommand.default.drawQRCode(b) } }, { key: "escDrawBitMap", value: function escDrawBitMap(a, b, c) { _esccommand.default.drawBitMap(a, b, c) } }, { key: "escNextLine", value: function escNextLine() { _esccommand.default.submitPrint() } }, { key: "escExcute", value: function escExcute(a) { this.writeBLE(_esccommand.default.getBuffer(), 0, a) } }, { key: "executeFirmwareUpdate", value: async function executeFirmwareUpdate(a, b) { var c = _otaUpdate.default.requestFirmwareUpdate(); this.writeBLE(c); var d = await this.listenValueChange(); this.sendFirmwareData(d, a, b) } }, { key: "sendFirmwareData", value: async function sendFirmwareData(a, b, c) { var d = _otaUpdate.default.sliceData(a, b); this.writeBLE(d); var e = await this.listenValueChange(); if (e && _otaUpdate.default.otaPacketIndex < b.length) { this.sendFirmwareData(a, b, c); var f = _otaUpdate.default.otaPacketIndex / b.length; if (c) { c(f, false) } } else { _otaUpdate.default.otaPacketIndex = 0; var g = _otaUpdate.default.executeFirmwareUpdate(b); this.writeBLE(g); var h = await this.listenValueChange(); if (c) { c(1.0, h) } } } }, { key: "removeTrailingZeros", value: function removeTrailingZeros(a) { var i = a.length - 1; while (i >= 0 && a[i] === 0) { a.pop(); i-- } return a } }, { key: "analysisWithData", value: function analysisWithData(a) { var b = new Uint8Array(a); if (b[0] == 0xdd & b[2] == 0x04) { if (b[3] == 0x82) { var c = b.slice(6, b[5]); var d = String.fromCharCode.apply(String, _toConsumableArray(this.removeTrailingZeros(Array.from(c)))); return d } } else if (b[0] == 0xdd & b[2] == 0x05) { if (b[3] == 0x82) { var e = b[6]; return e } else if (b[3] == 0x83) { var f = b[6] * 256 + b[7]; return f } else if (b[3] == 0x86) { var g = b[6]; return g } else if (b[3] == 0x87) { var h = b[6]; return h } else if (b[3] == 0x91) { var i = b[6]; return i } else if (b[3] == 0x92) { var j = b[6]; return j } else if (b[3] == 0xb1) { var k = b[6]; return k } else if (b[3] == 0xb3) { var l = b.slice(6, b.length - 2); var m = String.fromCharCode.apply(String, _toConsumableArray(this.removeTrailingZeros(Array.from(l)))); return m } else if (b[3] == 0xb4) { var n = b[6]; return n } else if (b[3] == 0xb5) { var o = b[6]; return o } else if (b[3] == 0xA1) { var p = b[6]; return p } } else if (b[0] == 0xdd & b[2] == 0x06) { if (b[3] == 0x82) { var q = 0; var r = 0; for (var s in _analysisFeature.hardwareFeatures) { if (_analysisFeature.hardwareFeatures[s].infoId == b[6]) { q = _analysisFeature.hardwareFeatures.indexOf(_analysisFeature.hardwareFeatures[s]) } if (_analysisFeature.hardwareFeatures[s].infoId == b[7]) { r = _analysisFeature.hardwareFeatures.indexOf(_analysisFeature.hardwareFeatures[s]); break } } var t = _analysisFeature.hardwareFeatures.slice(q, r + 1); var u = b.slice(8, b.length - 2); var v = {}; var w = 0; var x = _createForOfIteratorHelper(t), _step2; try { for (x.s(); !(_step2 = x.n()).done;) { var y = _step2.value; switch (y.infoId) { case 1: var z = u.slice(0, y.infoLen + 1); var A = String.fromCharCode.apply(String, _toConsumableArray(this.removeTrailingZeros(Array.from(z)))); v[y.infoKey] = A; break; case 2: v[y.infoKey] = u[w] == 1 ? '300' : '203'; break; case 3: case 4: case 5: case 6: v[y.infoKey] = u[w] == 1 ? 'true' : 'false'; break; case 7: v[y.infoKey] = "".concat(u[w]); break; case 8: v[y.infoKey] = "".concat((u[w] << 8) + u[w + 1]); break; case 9: v[y.infoKey] = u[w] == 2 ? 'Right' : u[w] == 1 ? 'Center' : 'Left'; break; default: break }w += y.infoLen } } catch (err) { x.e(err) } finally { x.f() } return v } else if (b[3] == 0x83) { var B = 0; var C = 0; var D = _createForOfIteratorHelper(_analysisFeature.firmwareFeatures), _step3; try { for (D.s(); !(_step3 = D.n()).done;) { var E = _step3.value; if (E.infoId == b[6]) { B = _analysisFeature.firmwareFeatures.indexOf(E) } if (E.infoId == b[7]) { C = _analysisFeature.firmwareFeatures.indexOf(E); break } } } catch (err) { D.e(err) } finally { D.f() } var F = _analysisFeature.firmwareFeatures.slice(B, C + 1); var G = b.slice(8, b.length - 2); var H = {}; var I = 0; var J = _createForOfIteratorHelper(F), _step4; try { for (J.s(); !(_step4 = J.n()).done;) { var K = _step4.value; switch (K.infoId) { case 1: var L = G[I] & 0x01; var M = G[I] & 0x02; var N = G[I] & 0x04; var O = G[I] & 0x08; var P = G[I] & 0x10; var Q = G[I] & 0x20; var R = G[I] & 0x40; var S = G[I] & 0x80; var T = G[I + 1] & 0x01; var U = G[I + 1] & 0x02; H['DeviceCover'] = L ? 'Open' : 'Close'; H['CarbonReady'] = M ? 'true' : 'false'; H['PaperReady'] = N ? 'true' : 'false'; H['CutterReady'] = O ? 'true' : 'false'; H['DevicePause'] = P ? 'true' : 'false'; H['DeviceIdle'] = Q ? 'true' : 'false'; H['SupportBeep'] = R ? 'true' : 'false'; H['DeviceOverHeat'] = S ? 'true' : 'false'; H['MemoryPrint'] = T ? 'On' : 'Off'; H['SmartVelocity'] = U ? 'On' : 'Off'; break; case 2: H[K.infoKey] = G[I] == 2 ? 'BlackMark' : G[I] == 1 ? 'Receipt' : 'Label'; break; case 3: case 4: case 5: H[K.infoKey] = "".concat(G[I]); break; case 6: H[K.infoKey] = "".concat((G[I] << 8) + G[I + 1]); break; case 7: H[K.infoKey] = "".concat(G[I]); break; case 8: case 9: case 10: case 11: H[K.infoKey] = "".concat(G[I]); break; case 12: H[K.infoKey] = "".concat((G[I] << 24) + (G[I + 1] << 16) + (G[I + 2] << 8) + G[I + 3]); break; default: break }I += K.infoLen } } catch (err) { J.e(err) } finally { J.f() } return H } else if (b[3] == 0x84) { var V = 0; var W = 0; var X = _createForOfIteratorHelper(_analysisFeature.displayFeatures), _step5; try { for (X.s(); !(_step5 = X.n()).done;) { var Y = _step5.value; if (Y.infoId == b[6]) { V = _analysisFeature.displayFeatures.indexOf(Y) } if (Y.infoId == b[7]) { W = _analysisFeature.displayFeatures.indexOf(Y); break } } } catch (err) { X.e(err) } finally { X.f() } var Z = _analysisFeature.displayFeatures.slice(V, W + 1); var bu = b.slice(8, b.length - 2); var bv = {}; var bw = 0; var bx = _createForOfIteratorHelper(Z), _step6; try { for (bx.s(); !(_step6 = bx.n()).done;) { var by = _step6.value; switch (by.infoId) { case 1: var bz = { 0x00: 'Label-Gap', 0x01: 'Receipt-Continuous', 0x02: 'Label-Continuous', 0x03: 'Label-BlackMark' }; bv[by.infoKey] = bz[bu[bw]]; break; case 2: case 3: case 4: var bA = bu.slice(bw, by.infoLen + bw); var bB = String.fromCharCode.apply(String, _toConsumableArray(this.removeTrailingZeros(Array.from(bA)))); bv[by.infoKey] = bB; break; case 5: bv[by.infoKey] = "".concat(bu[bw]); break; default: break }bw += by.infoLen } } catch (err) { bx.e(err) } finally { bx.f() } return bv } } else if (b[0] == 0xdd & b[2] == 0x01) { var bC = {}; if (b[3] == 0x81) { var bD = b[6]; var bE = bD & 0x01; var bF = !(bD & 0x04); var bG = bD & 0x10; var bH = !(bD & 0x20); var bI = bD & 0x80; bC = { DeviceCover: bE ? "Open" : "Close", PaperReady: bF, DevicePause: !!bG, DeviceIdle: !!bH, DeviceOverHeat: !!bI }; if (b[5] == 0x06) { var bJ = (a[10] & 0xFF) << 8; var bK = a[11] & 0xFF; var bL = bJ + bK - 0x8000; bC.PrintSerial = bL } } return bC } else if (b[0] == 0xdd & b[2] == 0x02) { if (b[3] == 0x82) { var bM = b[6] << 8; var bN = b[7]; return bM + bN } else if (b[3] == 0x83) { return b[6] == 0x00 } else if (b[3] == 0x84) { return b[6] == 0x00 } } } }]) }(); var _default = exports.default = new CTPL()